// Quick test to verify CTA export functionality
const moment = require('moment');

// Simulate the TimezoneHelper.formatForCSV function
function formatForCSV(date) {
  if (!date) return "N/A";
  
  try {
    const momentDate = moment.utc(date);
    
    if (!momentDate.isValid()) {
      return "Invalid Date";
    }
    
    // Convert UTC to IST (UTC+5:30) and format with 12-hour format
    const istDate = momentDate.utcOffset("+05:30");
    return istDate.format("DD-MM-YYYY hh:mm:ss A");
  } catch (error) {
    console.error("Error formatting date for CSV:", error);
    return "Invalid Date";
  }
}

// Test data similar to what would be in CTA analytics
const testData = [
  {
    id: 1,
    recipe_name: "Do not Update please",
    cta_type: "Contact Form",
    clicks: 13,
    last_clicked_at: "2025-07-17T12:00:00.000Z"
  },
  {
    id: 2,
    recipe_name: "Do not Update please", 
    cta_type: "Contact Info",
    clicks: 2,
    last_clicked_at: "2025-07-16T12:00:00.000Z"
  },
  {
    id: 3,
    recipe_name: "Do not Update please",
    cta_type: "Custom CTA", 
    clicks: 1,
    last_clicked_at: "2025-07-15T12:00:00.000Z"
  }
];

// Test CSV export format
console.log("=== CTA Analytics CSV Export Test ===");
console.log("ID,Recipe Name,CTA Type,Clicks,Last Clicked At");

testData.forEach(item => {
  const formattedTime = formatForCSV(item.last_clicked_at);
  console.log(`${item.id},"${item.recipe_name}","${item.cta_type}",${item.clicks},"${formattedTime}"`);
});

console.log("\n=== Time Format Test ===");
console.log("Original UTC:", "2025-07-17T12:00:00.000Z");
console.log("Formatted IST:", formatForCSV("2025-07-17T12:00:00.000Z"));
console.log("Expected: 17-07-2025 05:30:00 PM");
