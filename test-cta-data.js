// Quick script to add test CTA data
const { Sequelize, QueryTypes } = require('sequelize');

// Database connection (adjust as needed)
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: 'localhost',
  username: 'root',
  password: '',
  database: 'recipe_db',
  logging: false
});

async function addTestCtaData() {
  try {
    // First, let's check if there are any recipes
    const recipes = await sequelize.query(
      "SELECT id, recipe_title FROM mo_recipe WHERE recipe_status != 'deleted' LIMIT 3",
      { type: QueryTypes.SELECT }
    );

    if (recipes.length === 0) {
      console.log('No recipes found. Please create some recipes first.');
      return;
    }

    console.log('Found recipes:', recipes);

    // Add test CTA click data
    const testData = [
      {
        recipe_id: recipes[0].id,
        recipe_name: recipes[0].recipe_title,
        cta_type: 'contact_info',
        cta_text: 'Contact Us'
      },
      {
        recipe_id: recipes[0].id,
        recipe_name: recipes[0].recipe_title,
        cta_type: 'contact_form',
        cta_text: 'Get Recipe Details'
      },
      {
        recipe_id: recipes.length > 1 ? recipes[1].id : recipes[0].id,
        recipe_name: recipes.length > 1 ? recipes[1].recipe_title : recipes[0].recipe_title,
        cta_type: 'custom_cta',
        cta_text: 'Learn More'
      }
    ];

    for (const data of testData) {
      await sequelize.query(`
        INSERT INTO mo_recipe_analytics (
          event_type, 
          entity_type, 
          entity_id, 
          metadata, 
          created_at
        ) VALUES (
          'cta_click',
          'recipe',
          :recipe_id,
          :metadata,
          NOW()
        )
      `, {
        replacements: {
          recipe_id: data.recipe_id,
          metadata: JSON.stringify({
            recipe_name: data.recipe_name,
            cta_type: data.cta_type,
            cta_text: data.cta_text,
            tracking_source: 'test'
          })
        },
        type: QueryTypes.INSERT
      });
    }

    console.log('Test CTA data added successfully!');
    
    // Verify the data
    const count = await sequelize.query(
      "SELECT COUNT(*) as total FROM mo_recipe_analytics WHERE event_type = 'cta_click'",
      { type: QueryTypes.SELECT }
    );
    
    console.log('Total CTA clicks in database:', count[0].total);

  } catch (error) {
    console.error('Error adding test data:', error);
  } finally {
    await sequelize.close();
  }
}

addTestCtaData();
