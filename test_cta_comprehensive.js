const { exec } = require("child_process");
const util = require("util");
const execAsync = util.promisify(exec);

// Test configuration
const BASE_URL = "http://localhost:3000/api/v1/private/analytics";

// Helper function to make curl requests
async function makeRequest(endpoint, description) {
  console.log(`📊 ${description}`);
  console.log("=".repeat(50));

  try {
    const { stdout, stderr } = await execAsync(
      `curl -s -w "\\nHTTP_CODE:%{http_code}" "${BASE_URL}${endpoint}"`
    );

    const lines = stdout.split("\n");
    const httpCode = lines[lines.length - 1].replace("HTTP_CODE:", "");
    const responseBody = lines.slice(0, -1).join("\n");

    console.log(`✅ Status: ${httpCode}`);

    if (httpCode === "200") {
      try {
        const data = JSON.parse(responseBody);
        if (data.data && Array.isArray(data.data)) {
          console.log(`✅ Data count: ${data.data.length}`);

          if (data.data.length > 0) {
            console.log("📋 Sample data:");
            data.data.slice(0, 5).forEach((item, index) => {
              console.log(
                `  ${index + 1}. ${item.recipe_name || "N/A"} - ${item.cta_type || "N/A"} - ${item.clicks || 0} clicks - ${item.last_clicked_at || "N/A"}`
              );
            });
          }
        } else {
          console.log("📋 Response:", responseBody.substring(0, 200));
        }
      } catch (e) {
        console.log("📋 Raw response:", responseBody.substring(0, 200));
      }
    } else {
      console.log(`❌ Error: ${responseBody}`);
    }
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
  }

  console.log("");
}

async function runComprehensiveTests() {
  console.log("🧪 STARTING COMPREHENSIVE CTA ANALYTICS TESTS\n");

  try {
    // Test 1: Basic CTA Analytics Data
    await makeRequest("/cta-clicks", "TEST 1: Basic CTA Analytics Data");

    // Test 2: Sorting by Last Clicked At (DESC)
    await makeRequest(
      "/cta-clicks?sort_by=created_at&sort_order=desc",
      "TEST 2: Sorting by Last Clicked At (DESC)"
    );

    // Test 3: Sorting by Clicks (DESC)
    await makeRequest(
      "/cta-clicks?sort_by=clicks&sort_order=desc",
      "TEST 3: Sorting by Clicks (DESC)"
    );

    // Test 4: Filter by CTA Type - Contact Form
    await makeRequest(
      "/cta-clicks?cta_type=contact_form",
      "TEST 4: Filter by CTA Type - Contact Form"
    );

    // Test 5: Filter by CTA Type - Custom CTA
    await makeRequest(
      "/cta-clicks?cta_type=custom_cta",
      "TEST 5: Filter by CTA Type - Custom CTA"
    );

    // Test 6: Search Filter
    await makeRequest(
      "/cta-clicks?search=Masala",
      'TEST 6: Search Filter - "Masala"'
    );

    // Test 7: Combined Filters
    await makeRequest(
      "/cta-clicks?cta_type=contact_form&sort_by=clicks&sort_order=desc",
      "TEST 7: Combined Filters - Contact Form + Sort by Clicks"
    );

    console.log("🎉 ALL TESTS COMPLETED!");
    console.log("=".repeat(50));
    console.log("✅ Check the console output above for any issues");
    console.log("✅ Verify that:");
    console.log("   - All 4 entries are shown in basic data");
    console.log("   - CTA types show as 'Contact Form', 'Custom CTA', etc.");
    console.log("   - Sorting works correctly");
    console.log("   - Filters return expected results");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the tests
runComprehensiveTests();
